// Floor Entity (for API service)
export interface ApiFloor {
    id: number;
    name: string;
    code: string;
    level: number;
    description: string;
    displayName: string;
    isActive: boolean;
    hasAlerts: boolean;
    hasFloorPlan: boolean;
    totalRooms: number;
    totalDoors: number;
    deviceCount: number;
    totalMarkers: number;
    eventCount: number;
    building: {
        id: number;
        name: string;
        code: string;
    };
    zone: {
        id: number;
        name: string;
        code: string;
    };
    createdAt: string;
    updatedAt: string;
    buildingUrl: string;
    zonesUrl: string;
    roomsUrl: string;
    devicesUrl: string;
    markersUrl: string;
}

// Legacy Floor interface (for existing geography system) - keeping for backward compatibility
export interface LegacyFloor {
    id: number;
    name: string;
    level: number;
    zoneId: number;
    buildingId: number;
    floorCode: string; // e.g., 'F1', 'F2', 'B1'
    floorPlanUrl: string; // Default: '/plans/floorPlan-1.png'
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
}

// For backward compatibility, export LegacyFloor as Floor for existing code
export { LegacyFloor as Floor };

export interface FloorReference {
    id: number;
    name: string;
    level: number;
    floorCode: string;
    zoneId: number;
}

export interface FloorStats {
    floorId: number;
    floorName: string;
    floorCode: string;
    level: number;
    totalRooms: number;
    totalDoors: number;
}

// Pagination
export interface Pagination {
    limit: number;
    offset: number;
    has_more: boolean;
}

// API Response Data
export interface FloorsData {
    floors: ApiFloor[];
    total_count: number;
    returned_count: number;
    pagination: Pagination;
}

// Complete API response interface
export interface GetFloorsApiResponse {
    response_code: string;
    response_message: string;
    response_message_ar: string;
    data: FloorsData;
}

// Query parameters for filtering/pagination
export interface GetFloorsQueryParams {
    building_id?: number;
    zone_id?: number;
    level?: number;
    limit?: number;
    offset?: number;
    is_active?: boolean;
    search?: string;
}
