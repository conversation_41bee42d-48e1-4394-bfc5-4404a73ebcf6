import { useState, useEffect, useCallback } from 'react';
import { floorService } from './floor.service';
import type { Floor, GetFloorsApiResponse, GetFloorsQueryParams } from './floor.types';
import { logger } from '@/infrastructure/logging';

interface UseFloorsState {
    floors: Floor[];
    isLoading: boolean;
    error: string | null;
    totalCount: number;
    returnedCount: number;
    hasMore: boolean;
}

interface UseFloorsReturn extends UseFloorsState {
    refetch: (params?: GetFloorsQueryParams) => Promise<void>;
    getFloorById: (id: number) => Promise<Floor | null>;
    getFloorsByBuildingId: (buildingId: number, params?: Omit<GetFloorsQueryParams, 'building_id'>) => Promise<void>;
}

/**
 * React hook for managing floor data from the Floor Service
 * 
 * @param initialParams - Initial query parameters
 * @param autoFetch - Whether to automatically fetch data on mount (default: true)
 * @returns Floor data and management functions
 */
export function useFloors(
    initialParams: GetFloorsQueryParams = {},
    autoFetch: boolean = true
): UseFloorsReturn {
    const [state, setState] = useState<UseFloorsState>({
        floors: [],
        isLoading: false,
        error: null,
        totalCount: 0,
        returnedCount: 0,
        hasMore: false,
    });

    const updateState = useCallback((updates: Partial<UseFloorsState>) => {
        setState(prev => ({ ...prev, ...updates }));
    }, []);

    const handleApiResponse = useCallback((response: GetFloorsApiResponse) => {
        updateState({
            floors: response.data.floors,
            totalCount: response.data.total_count,
            returnedCount: response.data.returned_count,
            hasMore: response.data.pagination.has_more,
            isLoading: false,
            error: null,
        });
    }, [updateState]);

    const handleError = useCallback((error: unknown) => {
        const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
        logger.error('[useFloors] Error:', error as Error);
        updateState({
            isLoading: false,
            error: errorMessage,
        });
    }, [updateState]);

    const refetch = useCallback(async (params: GetFloorsQueryParams = {}) => {
        updateState({ isLoading: true, error: null });
        
        try {
            const response = await floorService.getFloors(params);
            handleApiResponse(response);
        } catch (error) {
            handleError(error);
        }
    }, [updateState, handleApiResponse, handleError]);

    const getFloorById = useCallback(async (id: number): Promise<Floor | null> => {
        try {
            const floor = await floorService.getFloorById(id);
            return floor;
        } catch (error) {
            logger.error(`[useFloors] Error fetching floor ${id}:`, error as Error);
            return null;
        }
    }, []);

    const getFloorsByBuildingId = useCallback(async (
        buildingId: number, 
        params: Omit<GetFloorsQueryParams, 'building_id'> = {}
    ) => {
        updateState({ isLoading: true, error: null });
        
        try {
            const response = await floorService.getFloorsByBuildingId(buildingId, params);
            handleApiResponse(response);
        } catch (error) {
            handleError(error);
        }
    }, [updateState, handleApiResponse, handleError]);

    // Auto-fetch on mount if enabled
    useEffect(() => {
        if (autoFetch) {
            refetch(initialParams);
        }
    }, [autoFetch, refetch]); // Note: initialParams intentionally excluded to prevent infinite loops

    return {
        ...state,
        refetch,
        getFloorById,
        getFloorsByBuildingId,
    };
}

/**
 * Hook specifically for getting floors by building ID
 * 
 * @param buildingId - The building ID to filter by
 * @param params - Additional query parameters
 * @param autoFetch - Whether to automatically fetch data on mount (default: true)
 */
export function useFloorsByBuilding(
    buildingId: number,
    params: Omit<GetFloorsQueryParams, 'building_id'> = {},
    autoFetch: boolean = true
) {
    return useFloors({ ...params, building_id: buildingId }, autoFetch);
}

/**
 * Hook for getting a single floor by ID
 * 
 * @param floorId - The floor ID to fetch
 * @param autoFetch - Whether to automatically fetch data on mount (default: true)
 */
export function useFloor(floorId: number | null, autoFetch: boolean = true) {
    const [floor, setFloor] = useState<Floor | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const fetchFloor = useCallback(async (id: number) => {
        setIsLoading(true);
        setError(null);
        
        try {
            const floorData = await floorService.getFloorById(id);
            setFloor(floorData);
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to fetch floor';
            setError(errorMessage);
            logger.error(`[useFloor] Error fetching floor ${id}:`, err as Error);
        } finally {
            setIsLoading(false);
        }
    }, []);

    useEffect(() => {
        if (autoFetch && floorId !== null) {
            fetchFloor(floorId);
        }
    }, [autoFetch, floorId, fetchFloor]);

    const refetch = useCallback(() => {
        if (floorId !== null) {
            return fetchFloor(floorId);
        }
        return Promise.resolve();
    }, [floorId, fetchFloor]);

    return {
        floor,
        isLoading,
        error,
        refetch,
    };
}
