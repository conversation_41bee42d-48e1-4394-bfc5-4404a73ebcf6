import { nebularApi } from '@/infrastructure/api/base/nebular-api.config';
import { logger } from '@/infrastructure/logging';
import { appConfig } from '@/shared/config/app.config';
import type { Floor, GetFloorsApiResponse, GetFloorsQueryParams } from './floor.types';

export class FloorService {
    private static instance: FloorService;
    private baseUrl: string;

    private constructor() {
        const config = appConfig.getConfig();
        this.baseUrl = config.nebularApi.baseUrl;
    }

    public static getInstance(): FloorService {
        if (!FloorService.instance) {
            FloorService.instance = new FloorService();
        }
        return FloorService.instance;
    }

    /**
     * Fetch floors with optional query parameters
     */
    public async getFloors(params: GetFloorsQueryParams = {}): Promise<GetFloorsApiResponse> {
        logger.info('[FloorService] Fetching floors with params:', params);

        try {
            const config = appConfig.getConfig();

            // Use mock data in development if enabled
            if (config.nebularApi.enableMockData) {
                return this.getMockFloors(params);
            }

            return this.getRealFloors(params);
        } catch (error: unknown) {
            logger.error('[FloorService] Error fetching floors:', error as Error);
            throw error;
        }
    }

    /**
     * Fetch floors by building ID
     */
    public async getFloorsByBuildingId(buildingId: number, params: Omit<GetFloorsQueryParams, 'building_id'> = {}): Promise<GetFloorsApiResponse> {
        return this.getFloors({ ...params, building_id: buildingId });
    }

    /**
     * Fetch a single floor by ID
     */
    public async getFloorById(id: number): Promise<Floor> {
        logger.info(`[FloorService] Fetching floor with ID: ${id}`);

        try {
            const response = await nebularApi.get<{ data: Floor }>(`${this.baseUrl}/api/v1/floors/${id}`);

            return response.data.data;
        } catch (error: unknown) {
            logger.error(`[FloorService] Error fetching floor ${id}:`, error as Error);
            throw error;
        }
    }

    /**
     * Real API call to fetch floors
     */
    private async getRealFloors(params: GetFloorsQueryParams = {}): Promise<GetFloorsApiResponse> {
        const queryParams = new URLSearchParams();
        
        // Add query parameters
        if (params.building_id) queryParams.append('building_id', params.building_id.toString());
        if (params.zone_id) queryParams.append('zone_id', params.zone_id.toString());
        if (params.level) queryParams.append('level', params.level.toString());
        if (params.limit) queryParams.append('limit', params.limit.toString());
        if (params.offset) queryParams.append('offset', params.offset.toString());
        if (params.is_active !== undefined) queryParams.append('is_active', params.is_active.toString());
        if (params.search) queryParams.append('search', params.search);

        const url = `${this.baseUrl}/api/v1/floors${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
        const response = await nebularApi.get<GetFloorsApiResponse>(url);

        // Basic response validation
        if (!response.data || !response.data.data || !Array.isArray(response.data.data.floors)) {
            logger.error('[FloorService] Invalid response format from floors API');
            throw new Error('Invalid API response format');
        }

        logger.info(`[FloorService] Successfully fetched ${response.data.data.returned_count} floors`);
        return response.data;
    }

    /**
     * Mock data for development/testing
     */
    private getMockFloors(params: GetFloorsQueryParams = {}): Promise<GetFloorsApiResponse> {
        logger.info('[FloorService] Using mock data for floors');

        // Filter mock data based on building_id if provided
        let mockFloors = this.getAllMockFloors();
        
        if (params.building_id) {
            mockFloors = mockFloors.filter(floor => floor.building.id === params.building_id);
        }
        
        if (params.zone_id) {
            mockFloors = mockFloors.filter(floor => floor.zone.id === params.zone_id);
        }
        
        if (params.level) {
            mockFloors = mockFloors.filter(floor => floor.level === params.level);
        }
        
        if (params.is_active !== undefined) {
            mockFloors = mockFloors.filter(floor => floor.isActive === params.is_active);
        }

        const mockResponse: GetFloorsApiResponse = {
            response_code: '000',
            response_message: 'Success',
            response_message_ar: 'نجح',
            data: {
                floors: mockFloors,
                total_count: mockFloors.length,
                returned_count: mockFloors.length,
                pagination: {
                    limit: params.limit || 100,
                    offset: params.offset || 0,
                    has_more: false,
                },
            },
        };
        
        return Promise.resolve(mockResponse);
    }

    /**
     * Get all mock floors data
     */
    private getAllMockFloors(): Floor[] {
        return [
            {
                id: 1,
                name: "Floor1",
                code: "F1",
                level: 1,
                description: "Ground floor with lobby, reception, and meeting rooms",
                displayName: "Building A - North Wing - Floor 1",
                isActive: true,
                hasAlerts: false,
                hasFloorPlan: false,
                totalRooms: 2,
                totalDoors: 2,
                deviceCount: 8,
                totalMarkers: 0,
                eventCount: 16,
                building: {
                    id: 1,
                    name: "Building A",
                    code: "A"
                },
                zone: {
                    id: 1,
                    name: "North Wing",
                    code: "NW"
                },
                createdAt: "2025-09-23T10:47:38.352278",
                updatedAt: "2025-09-23T10:47:38.352278",
                buildingUrl: "/api/v1/buildings/1",
                zonesUrl: "/api/v1/zones/1",
                roomsUrl: "/api/v1/floors/1/rooms",
                devicesUrl: "/api/v1/floors/1/devices",
                markersUrl: "/api/v1/floors/1/markers"
            },
            {
                id: 2,
                name: "Floor2",
                code: "F2",
                level: 2,
                description: "First floor with office spaces and conference rooms",
                displayName: "Building A - North Wing - Floor 2",
                isActive: true,
                hasAlerts: false,
                hasFloorPlan: false,
                totalRooms: 1,
                totalDoors: 0,
                deviceCount: 1,
                totalMarkers: 0,
                eventCount: 4,
                building: {
                    id: 1,
                    name: "Building A",
                    code: "A"
                },
                zone: {
                    id: 1,
                    name: "North Wing",
                    code: "NW"
                },
                createdAt: "2025-09-23T10:47:38.352278",
                updatedAt: "2025-09-23T10:47:38.352278",
                buildingUrl: "/api/v1/buildings/1",
                zonesUrl: "/api/v1/zones/1",
                roomsUrl: "/api/v1/floors/2/rooms",
                devicesUrl: "/api/v1/floors/2/devices",
                markersUrl: "/api/v1/floors/2/markers"
            },
            {
                id: 3,
                name: "Floor3",
                code: "F3",
                level: 3,
                description: "Basement level with server room and utilities",
                displayName: "Building A - North Wing - Floor 3",
                isActive: true,
                hasAlerts: false,
                hasFloorPlan: false,
                totalRooms: 1,
                totalDoors: 0,
                deviceCount: 1,
                totalMarkers: 0,
                eventCount: 2,
                building: {
                    id: 1,
                    name: "Building A",
                    code: "A"
                },
                zone: {
                    id: 1,
                    name: "North Wing",
                    code: "NW"
                },
                createdAt: "2025-09-23T10:47:38.352278",
                updatedAt: "2025-09-23T10:47:38.352278",
                buildingUrl: "/api/v1/buildings/1",
                zonesUrl: "/api/v1/zones/1",
                roomsUrl: "/api/v1/floors/3/rooms",
                devicesUrl: "/api/v1/floors/3/devices",
                markersUrl: "/api/v1/floors/3/markers"
            }
        ];
    }
}

// Export singleton instance
export const floorService = FloorService.getInstance();
