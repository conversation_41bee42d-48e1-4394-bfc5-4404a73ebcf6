import { floorService } from './floor.service';
import type { GetFloorsQueryParams } from './floor.types';

/**
 * Test script for FloorService
 * This can be run to verify the floor service works correctly
 */
export async function testFloorService() {
    console.log('🧪 Testing Floor Service...\n');

    try {
        // Test 1: Get all floors
        console.log('📋 Test 1: Get all floors');
        const allFloors = await floorService.getFloors();
        console.log(`✅ Retrieved ${allFloors.data.returned_count} floors`);
        console.log(`📊 Response code: ${allFloors.response_code}`);
        console.log(`📝 Response message: ${allFloors.response_message}\n`);

        // Test 2: Get floors by building ID
        console.log('🏢 Test 2: Get floors by building ID (building_id=1)');
        const buildingFloors = await floorService.getFloorsByBuildingId(1);
        console.log(`✅ Retrieved ${buildingFloors.data.returned_count} floors for building 1`);
        buildingFloors.data.floors.forEach(floor => {
            console.log(`  - ${floor.displayName} (Level ${floor.level})`);
        });
        console.log('');

        // Test 3: Get floors with query parameters
        console.log('🔍 Test 3: Get floors with query parameters (building_id=1, level=1)');
        const queryParams: GetFloorsQueryParams = {
            building_id: 1,
            level: 1,
            limit: 10
        };
        const filteredFloors = await floorService.getFloors(queryParams);
        console.log(`✅ Retrieved ${filteredFloors.data.returned_count} floors matching criteria`);
        filteredFloors.data.floors.forEach(floor => {
            console.log(`  - ${floor.name} (${floor.code}) - Level ${floor.level}`);
            console.log(`    Building: ${floor.building.name} (${floor.building.code})`);
            console.log(`    Zone: ${floor.zone.name} (${floor.zone.code})`);
            console.log(`    Rooms: ${floor.totalRooms}, Doors: ${floor.totalDoors}, Devices: ${floor.deviceCount}`);
        });
        console.log('');

        // Test 4: Get single floor by ID
        console.log('🎯 Test 4: Get single floor by ID (id=1)');
        try {
            const singleFloor = await floorService.getFloorById(1);
            console.log(`✅ Retrieved floor: ${singleFloor.displayName}`);
            console.log(`📍 Description: ${singleFloor.description}`);
            console.log(`🔗 URLs:`);
            console.log(`  - Building: ${singleFloor.buildingUrl}`);
            console.log(`  - Zones: ${singleFloor.zonesUrl}`);
            console.log(`  - Rooms: ${singleFloor.roomsUrl}`);
            console.log(`  - Devices: ${singleFloor.devicesUrl}`);
            console.log(`  - Markers: ${singleFloor.markersUrl}`);
        } catch (error) {
            console.log(`⚠️  Single floor test skipped (requires real API): ${error}`);
        }

        console.log('\n🎉 All tests completed successfully!');
        return true;

    } catch (error) {
        console.error('❌ Test failed:', error);
        return false;
    }
}

// Export for use in other test files
export { floorService };
