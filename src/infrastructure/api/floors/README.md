# Floor Service

A service for managing floor data from the Nebular API, similar to the Building Service.

## Features

- Fetch all floors with optional filtering
- Get floors by building ID
- Get floors by zone ID
- Get single floor by ID
- Support for pagination and search
- Mock data support for development
- TypeScript support with full type definitions

## API Endpoint

The service connects to: `http://localhost:8069/api/v1/floors`

## Usage

### Basic Usage

```typescript
import { floorService } from '@/infrastructure/api/floors';

// Get all floors
const allFloors = await floorService.getFloors();

// Get floors by building ID
const buildingFloors = await floorService.getFloorsByBuildingId(1);

// Get single floor by ID
const floor = await floorService.getFloorById(1);
```

### Advanced Filtering

```typescript
import { floorService, type GetFloorsQueryParams } from '@/infrastructure/api/floors';

// Filter floors with query parameters
const params: GetFloorsQueryParams = {
    building_id: 1,
    zone_id: 1,
    level: 2,
    is_active: true,
    limit: 10,
    offset: 0,
    search: 'office'
};

const filteredFloors = await floorService.getFloors(params);
```

### Response Structure

The API returns data in this format:

```typescript
{
    "response_code": "000",
    "response_message": "Success",
    "response_message_ar": "نجح",
    "data": {
        "floors": [
            {
                "id": 1,
                "name": "Floor1",
                "code": "F1",
                "level": 1,
                "description": "Ground floor with lobby, reception, and meeting rooms",
                "displayName": "Building A - North Wing - Floor 1",
                "isActive": true,
                "hasAlerts": false,
                "hasFloorPlan": false,
                "totalRooms": 2,
                "totalDoors": 2,
                "deviceCount": 8,
                "totalMarkers": 0,
                "eventCount": 16,
                "building": {
                    "id": 1,
                    "name": "Building A",
                    "code": "A"
                },
                "zone": {
                    "id": 1,
                    "name": "North Wing",
                    "code": "NW"
                },
                "createdAt": "2025-09-23T10:47:38.352278",
                "updatedAt": "2025-09-23T10:47:38.352278",
                "buildingUrl": "/api/v1/buildings/1",
                "zonesUrl": "/api/v1/zones/1",
                "roomsUrl": "/api/v1/floors/1/rooms",
                "devicesUrl": "/api/v1/floors/1/devices",
                "markersUrl": "/api/v1/floors/1/markers"
            }
        ],
        "total_count": 3,
        "returned_count": 3,
        "pagination": {
            "limit": 100,
            "offset": 0,
            "has_more": false
        }
    }
}
```

## Configuration

The service uses the same configuration as other Nebular API services:

- **Mock Data**: Controlled by `config.nebularApi.enableMockData`
- **Base URL**: Set via `config.nebularApi.baseUrl`

## Testing

Run the test script to verify the service works:

```typescript
import { testFloorService } from '@/infrastructure/api/floors/floor.service.test';

await testFloorService();
```

## Error Handling

The service includes comprehensive error handling and logging:

```typescript
try {
    const floors = await floorService.getFloors();
    // Handle success
} catch (error) {
    console.error('Failed to fetch floors:', error);
    // Handle error
}
```

## Integration with Existing Systems

This service is designed to work alongside the existing geography system. The floor types include both the new API format and legacy format for backward compatibility.
