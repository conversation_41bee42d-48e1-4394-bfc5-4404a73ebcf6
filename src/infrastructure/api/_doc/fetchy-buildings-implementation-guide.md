# Fetchy Library Implementation Guide - Buildings API

## Overview

This guide demonstrates how to implement the <PERSON>tchy library to interact with the Buildings API endpoint (`http://localhost:8088/api/v1/buildings`) following the established PortService pattern. This implementation provides a clean, type-safe, and maintainable way to handle API calls with built-in error handling, logging, and mock data support.

## Prerequisites

- Fetchy library configured in your project
- App configuration setup with nebularApi base URL
- TypeScript environment

## Configuration

### Base URL Configuration

The Buildings API uses the `nebularApi` configuration from <mcfile name="app.config.ts" path="/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/next_nublar_lc/src/shared/config/app.config.ts"></mcfile>:

```typescript
nebularApi: {
    baseUrl: process.env.NEXT_PUBLIC_NEBULAR_API_BASE_URL || 'http://localhost:8088',
    timeout: parseInt(process.env.NEXT_PUBLIC_NEBULAR_API_TIMEOUT || '30000', 10),
    retryAttempts: parseInt(process.env.NEXT_PUBLIC_NEBULAR_API_RETRY_ATTEMPTS || '3', 10),
    retryDelay: parseInt(process.env.NEXT_PUBLIC_NEBULAR_API_RETRY_DELAY || '1000', 10),
}
```

Environment variables in <mcfile name="env" path="/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/next_nublar_lc/.env"></mcfile>:
```env
NEXT_PUBLIC_NEBULAR_API_BASE_URL=http://localhost:8088
NEXT_PUBLIC_NEBULAR_API_TIMEOUT=30000
NEXT_PUBLIC_NEBULAR_API_RETRY_ATTEMPTS=3
NEXT_PUBLIC_NEBULAR_API_RETRY_DELAY=1000
```

## TypeScript Interfaces

### API Response Types

```typescript
// Building entity interface
export interface Building {
    id: number;
    name: string;
    code: string;
    description: string;
    address: string;
    is_active: boolean;
    zone_count: number;
    floor_count: number;
    room_count: number;
    door_count: number;
    device_count: number;
    event_count: number;
    has_alerts: boolean;
    create_date: string;
    write_date: string;
    zones_url: string;
    floors_url: string;
    devices_url: string;
}

// Pagination interface
export interface Pagination {
    limit: number;
    offset: number;
    has_more: boolean;
}

// Buildings data wrapper
export interface BuildingsData {
    buildings: Building[];
    total_count: number;
    returned_count: number;
    pagination: Pagination;
}

// Complete API response interface
export interface GetBuildingsApiResponse {
    response_code: string;
    response_message: string;
    response_message_ar: string;
    data: BuildingsData;
}

// Query parameters for filtering/pagination
export interface GetBuildingsQueryParams {
    limit?: number;
    offset?: number;
    is_active?: boolean;
    search?: string;
}
```

## Service Implementation

### BuildingService Class

```typescript
// src/infrastructure/api/buildings/building.service.ts
import { fetchy } from '@/shared/lib/Fetchy';
import { logger } from '@/infrastructure/logging';
import { appConfig } from '@/shared/config/app.config';
import type { 
    Building, 
    GetBuildingsApiResponse, 
    GetBuildingsQueryParams 
} from './building.types';

export class BuildingService {
    private static instance: BuildingService;
    private baseUrl: string;

    private constructor() {
        const config = appConfig.getConfig();
        this.baseUrl = config.nebularApi.baseUrl;
    }

    public static getInstance(): BuildingService {
        if (!BuildingService.instance) {
            BuildingService.instance = new BuildingService();
        }
        return BuildingService.instance;
    }

    /**
     * Fetch all buildings with optional query parameters
     */
    public async getBuildings(params: GetBuildingsQueryParams = {}): Promise<GetBuildingsApiResponse> {
        logger.info('[BuildingService] Fetching buildings with params:', params);

        try {
            const config = appConfig.getConfig();
            
            // Use mock data in development if enabled
            if (config.features.enableDebugTools && process.env.NEXT_PUBLIC_ENABLE_MOCK_DATA === 'true') {
                return this.getMockBuildings(params);
            }
            
            return this.getRealBuildings(params);
        } catch (error: unknown) {
            logger.error('[BuildingService] Error fetching buildings:', error as Error);
            throw error;
        }
    }

    /**
     * Fetch a single building by ID
     */
    public async getBuildingById(id: number): Promise<Building> {
        logger.info(`[BuildingService] Fetching building with ID: ${id}`);

        try {
            const response = await fetchy.get<{ data: Building }>(
                `${this.baseUrl}/api/v1/buildings/${id}`
            );
            
            return response.data.data;
        } catch (error: unknown) {
            logger.error(`[BuildingService] Error fetching building ${id}:`, error as Error);
            throw error;
        }
    }

    /**
     * Real API call to fetch buildings
     */
    private async getRealBuildings(params: GetBuildingsQueryParams = {}): Promise<GetBuildingsApiResponse> {
        const response = await fetchy.get<GetBuildingsApiResponse>(
            `${this.baseUrl}/api/v1/buildings`,
            { params }
        );

        // Basic response validation
        if (!response.data || !response.data.data || !Array.isArray(response.data.data.buildings)) {
            logger.error('[BuildingService] Invalid response format from buildings API');
            throw new Error('Invalid API response format');
        }

        logger.info(`[BuildingService] Successfully fetched ${response.data.data.returned_count} buildings`);
        return response.data;
    }

    /**
     * Mock data for development/testing
     */
    private getMockBuildings(params: GetBuildingsQueryParams = {}): Promise<GetBuildingsApiResponse> {
        logger.info('[BuildingService] Using mock data for buildings');
        
        const mockResponse: GetBuildingsApiResponse = {
            response_code: "000",
            response_message: "Success",
            response_message_ar: "نجح",
            data: {
                buildings: [
                    {
                        id: 1,
                        name: "Building A",
                        code: "A",
                        description: "Main headquarters building with offices, meeting rooms, and data center",
                        address: "123 Technology Drive, Innovation City, IC 12345",
                        is_active: true,
                        zone_count: 2,
                        floor_count: 3,
                        room_count: 4,
                        door_count: 2,
                        device_count: 10,
                        event_count: 23,
                        has_alerts: false,
                        create_date: "2025-09-21T09:52:36.320484",
                        write_date: "2025-09-21T09:52:36.320484",
                        zones_url: "/api/v1/buildings/1/zones",
                        floors_url: "/api/v1/buildings/1/floors",
                        devices_url: "/api/v1/buildings/1/devices"
                    },
                    {
                        id: 2,
                        name: "Building B",
                        code: "B",
                        description: "Primary distribution and logistics center",
                        address: "456 Logistics Boulevard, Industrial Park, IP 67890",
                        is_active: true,
                        zone_count: 1,
                        floor_count: 1,
                        room_count: 1,
                        door_count: 1,
                        device_count: 2,
                        event_count: 5,
                        has_alerts: false,
                        create_date: "2025-09-21T09:52:36.320484",
                        write_date: "2025-09-21T09:52:36.320484",
                        zones_url: "/api/v1/buildings/2/zones",
                        floors_url: "/api/v1/buildings/2/floors",
                        devices_url: "/api/v1/buildings/2/devices"
                    }
                ],
                total_count: 2,
                returned_count: 2,
                pagination: {
                    limit: params.limit || 100,
                    offset: params.offset || 0,
                    has_more: false
                }
            }
        };

        return Promise.resolve(mockResponse);
    }
}

// Export singleton instance
export const buildingService = BuildingService.getInstance();
```

## Store Integration

### Using BuildingService in building.store.ts

The <mcfile name="building.store.ts" path="/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/next_nublar_lc/src/stores/building.store.ts"></mcfile> can be enhanced to use the BuildingService:

```typescript
// Enhanced building.store.ts with BuildingService integration
import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';
import { logger } from '@/infrastructure/logging';
import { buildingService } from '@/infrastructure/api/buildings/building.service';
import type { Building } from '@/infrastructure/api/geography/buildings/types';

interface BuildingState {
    buildings: Building[];
    selectedBuilding: Building | null;
    isLoading: boolean;
    error: string | null;
}

interface BuildingActions {
    loadBuildings: () => Promise<void>;
    refreshBuildings: () => Promise<void>;
    setSelectedBuilding: (building: Building | null) => void;
    clearSelectedBuilding: () => void;
    clearError: () => void;
}

const _buildingStore = (instanceId: string): StateCreator<BuildingState & BuildingActions> => {
    return (set, get) => ({
        // State
        buildings: [],
        selectedBuilding: null,
        isLoading: false,
        error: null,

        // Actions
        loadBuildings: async () => {
            if (get().isLoading) return;
            
            set({ isLoading: true, error: null });
            
            try {
                const response = await buildingService.getBuildings();
                const buildings = response.data.buildings;
                
                set({
                    buildings,
                    selectedBuilding: buildings.length > 0 ? buildings[0] : null,
                    isLoading: false,
                    error: null
                });
                
                logger.info(`buildingStore(${instanceId}): loadBuildings: loaded ${buildings.length} buildings`);
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to load buildings';
                set({
                    buildings: [],
                    selectedBuilding: null,
                    isLoading: false,
                    error: errorMessage
                });
                logger.error(`buildingStore(${instanceId}): loadBuildings: error:`, error as Error);
            }
        },

        refreshBuildings: async () => {
            // Force refresh by clearing current data first
            set({ buildings: [], selectedBuilding: null });
            await get().loadBuildings();
        },

        setSelectedBuilding: (building: Building | null) => {
            set({ selectedBuilding: building });
            logger.info(`buildingStore(${instanceId}): setSelectedBuilding:`, building);
        },

        clearSelectedBuilding: () => {
            set({ selectedBuilding: null });
            logger.info(`buildingStore(${instanceId}): clearSelectedBuilding`);
        },

        clearError: () => {
            set({ error: null });
        }
    });
};
```

## React Component Integration Examples

### Example 1: Simple Buildings List Component

```tsx
// src/components/buildings/BuildingsList.tsx
'use client';

import React, { useState } from 'react';
import { useFetchyQuery } from '@/shared/lib/Fetchy';
import type { GetBuildingsApiResponse, GetBuildingsQueryParams } from '@/infrastructure/api/buildings/building.types';

export function BuildingsList() {
    const [queryParams, setQueryParams] = useState<GetBuildingsQueryParams>({
        limit: 10,
        offset: 0
    });

    const { 
        data, 
        isLoading, 
        error, 
        refetch 
    } = useFetchyQuery<GetBuildingsApiResponse>(
        ['buildings', JSON.stringify(queryParams)],
        '/api/v1/buildings',
        { params: queryParams as Record<string, unknown> },
        {
            staleTime: 5 * 60 * 1000, // 5 minutes
            cacheTime: 10 * 60 * 1000, // 10 minutes
        }
    );

    if (isLoading) {
        return <div className="p-4">Loading buildings...</div>;
    }

    if (error) {
        return (
            <div className="p-4 text-red-600">
                Error loading buildings: {error.message}
                <button 
                    onClick={() => refetch()} 
                    className="ml-2 px-3 py-1 bg-blue-500 text-white rounded"
                >
                    Retry
                </button>
            </div>
        );
    }

    const buildings = data?.data?.buildings || [];

    return (
        <div className="p-4">
            <h2 className="text-2xl font-bold mb-4">Buildings</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {buildings.map((building) => (
                    <div key={building.id} className="border border-gray-200 rounded-lg p-4">
                        <h3 className="font-semibold text-lg">{building.name}</h3>
                        <p className="text-gray-600 text-sm mb-2">{building.code}</p>
                        <p className="text-gray-700 mb-2">{building.description}</p>
                        <div className="mt-2 flex flex-wrap gap-2">
                            <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                                {building.zone_count} zones
                            </span>
                            <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded">
                                {building.floor_count} floors
                            </span>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
}
```

### Example 2: Using BuildingService with Custom Hook

```tsx
// src/components/buildings/BuildingsWithService.tsx
'use client';

import React from 'react';
import { useFetchyQuery } from '@/shared/lib/Fetchy';
import { buildingService } from '@/infrastructure/api/buildings/building.service';
import type { GetBuildingsApiResponse, GetBuildingsQueryParams } from '@/infrastructure/api/buildings/building.types';

export function BuildingsWithService() {
    const params: GetBuildingsQueryParams = { limit: 5 };

    const { 
        data, 
        isLoading, 
        error 
    } = useFetchyQuery<GetBuildingsApiResponse>(
        ['buildings-service', JSON.stringify(params)],
        '', // Empty endpoint since we're using queryFn
        {},
        {
            queryFn: () => buildingService.getBuildings(params),
            staleTime: 5 * 60 * 1000,
        }
    );

    if (isLoading) return <div>Loading...</div>;
    if (error) return <div>Error: {error.message}</div>;

    return (
        <div className="p-4">
            <h2 className="text-xl font-bold mb-4">Buildings (Service Layer)</h2>
            <div className="space-y-2">
                {data?.data?.buildings?.map((building) => (
                    <div key={building.id} className="p-3 border rounded">
                        <strong>{building.name}</strong> - {building.description}
                    </div>
                ))}
            </div>
        </div>
    );
}
```

### Example 3: Store Integration Component

```tsx
// src/components/buildings/BuildingsFromStore.tsx
'use client';

import React, { useEffect } from 'react';
import { useBuildingStore } from '@/stores/building.store';

export function BuildingsFromStore() {
    const { 
        buildings, 
        selectedBuilding, 
        isLoading, 
        error,
        loadBuildings, 
        setSelectedBuilding,
        clearError 
    } = useBuildingStore();

    useEffect(() => {
        if (buildings.length === 0 && !isLoading) {
            loadBuildings();
        }
    }, [buildings.length, isLoading, loadBuildings]);

    if (isLoading) {
        return <div className="p-4">Loading buildings from store...</div>;
    }

    if (error) {
        return (
            <div className="p-4 text-red-600">
                Error: {error}
                <button 
                    onClick={() => {
                        clearError();
                        loadBuildings();
                    }}
                    className="ml-2 px-3 py-1 bg-blue-500 text-white rounded"
                >
                    Retry
                </button>
            </div>
        );
    }

    return (
        <div className="p-4">
            <h2 className="text-2xl font-bold mb-4">Buildings from Store</h2>
            
            {/* Buildings Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                {buildings.map((building) => (
                    <div 
                        key={building.id} 
                        className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                            selectedBuilding?.id === building.id 
                                ? 'border-blue-500 bg-blue-50' 
                                : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => setSelectedBuilding(building)}
                    >
                        <h3 className="font-semibold text-lg">{building.name}</h3>
                        <p className="text-gray-600 text-sm">{building.code}</p>
                        <p className="text-gray-700 text-sm mt-1">{building.description}</p>
                        <div className="mt-2 flex gap-2">
                            <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                                {building.device_count} devices
                            </span>
                            <span className={`px-2 py-1 text-xs rounded ${
                                building.is_active 
                                    ? 'bg-green-100 text-green-800' 
                                    : 'bg-red-100 text-red-800'
                            }`}>
                                {building.is_active ? 'Active' : 'Inactive'}
                            </span>
                        </div>
                    </div>
                ))}
            </div>

            {/* Selected Building Details */}
            {selectedBuilding && (
                <div className="border-t pt-4">
                    <h3 className="text-lg font-semibold mb-2">Selected Building Details</h3>
                    <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="font-medium">{selectedBuilding.name}</h4>
                        <p className="text-sm text-gray-600 mb-2">{selectedBuilding.address}</p>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div><strong>Zones:</strong> {selectedBuilding.zone_count}</div>
                            <div><strong>Floors:</strong> {selectedBuilding.floor_count}</div>
                            <div><strong>Rooms:</strong> {selectedBuilding.room_count}</div>
                            <div><strong>Devices:</strong> {selectedBuilding.device_count}</div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}
```

## Error Handling

### Custom Error Types

```typescript
// src/infrastructure/api/buildings/building.errors.ts
export class BuildingApiError extends Error {
    constructor(
        message: string,
        public code: string,
        public status?: number
    ) {
        super(message);
        this.name = 'BuildingApiError';
    }
}

export class BuildingNotFoundError extends BuildingApiError {
    constructor(id: number) {
        super(`Building with ID ${id} not found`, 'BUILDING_NOT_FOUND', 404);
    }
}

export class BuildingValidationError extends BuildingApiError {
    constructor(message: string, public details?: any) {
        super(message, 'VALIDATION_ERROR', 400);
    }
}
```

## Best Practices

1. **Type Safety**: Always define proper TypeScript interfaces for API responses
2. **Error Handling**: Implement comprehensive error handling with custom error types
3. **Logging**: Use consistent logging throughout the service for debugging
4. **Mock Data**: Provide mock data for development and testing
5. **Singleton Pattern**: Use singleton pattern for service instances to avoid multiple API clients
6. **Configuration**: Use centralized configuration for API endpoints and settings
7. **Caching**: Leverage React Query's caching capabilities for better performance
8. **Validation**: Implement basic response validation without heavy schema libraries

## File Structure

```
src/
├── infrastructure/
│   └── api/
│       ├── _doc/
│       │   └── fetchy-buildings-implementation-guide.md
│       └── buildings/
│           ├── building.service.ts
│           ├── building.types.ts
│           └── building.errors.ts
├── components/
│   └── buildings/
│       ├── BuildingsList.tsx
│       ├── BuildingsWithService.tsx
│       └── BuildingsFromStore.tsx
├── stores/
│   └── building.store.ts
└── shared/
    └── config/
        └── app.config.ts
```

This implementation provides a robust, maintainable, and type-safe way to interact with the Buildings API using the Fetchy library, following established patterns and best practices.