// import { fetchy } from '@/shared/lib/Fetchy';
import { nebularApi } from '@/infrastructure/api/base/nebular-api.config';
import { logger } from '@/infrastructure/logging';
import { appConfig } from '@/shared/config/app.config';
import type { Building, GetBuildingsApiResponse, GetBuildingsQueryParams } from './building.types';

export class BuildingService {
    private static instance: BuildingService;
    private baseUrl: string;

    private constructor() {
        const config = appConfig.getConfig();
        this.baseUrl = config.nebularApi.baseUrl;
    }

    public static getInstance(): BuildingService {
        if (!BuildingService.instance) {
            BuildingService.instance = new BuildingService();
        }
        return BuildingService.instance;
    }

    /**
     * Fetch all buildings with optional query parameters
     */
    public async getBuildings(params: GetBuildingsQueryParams = {}): Promise<GetBuildingsApiResponse> {
        logger.info('[BuildingService] Fetching buildings with params:', params);

        try {
            const config = appConfig.getConfig();

            // Use mock data in development if enabled
            if (config.nebularApi.enableMockData) {
                return this.getMockBuildings(params);
            }

            return this.getRealBuildings();
        } catch (error: unknown) {
            logger.error('[BuildingService] Error fetching buildings:', error as Error);
            throw error;
        }
    }

    /**
     * Fetch a single building by ID
     */
    public async getBuildingById(id: number): Promise<Building> {
        logger.info(`[BuildingService] Fetching building with ID: ${id}`);

        try {
            const response = await nebularApi.get<{ data: Building }>(`${this.baseUrl}/api/v1/buildings/${id}`);

            return response.data.data;
        } catch (error: unknown) {
            logger.error(`[BuildingService] Error fetching building ${id}:`, error as Error);
            throw error;
        }
    }

    /**
     * Real API call to fetch buildings
     */
    private async getRealBuildings(): Promise<GetBuildingsApiResponse> {
        const response = await nebularApi.get<GetBuildingsApiResponse>(
            `${this.baseUrl}/api/v1/buildings`,
            // { params }
        );

        // Basic response validation
        if (!response.data || !response.data.data || !Array.isArray(response.data.data.buildings)) {
            logger.error('[BuildingService] Invalid response format from buildings API');
            throw new Error('Invalid API response format');
        }

        logger.info(`[BuildingService] Successfully fetched ${response.data.data.returned_count} buildings`);
        return response.data;
    }

    /**
     * Mock data for development/testing
     */
    private getMockBuildings(params: GetBuildingsQueryParams = {}): Promise<GetBuildingsApiResponse> {
        logger.info('[BuildingService] Using mock data for buildings');

        const mockResponse: GetBuildingsApiResponse = {
            response_code: '000',
            response_message: 'Success',
            response_message_ar: 'نجح',
            data: {
                buildings: [
                    {
                        id: 1,
                        name: 'Building A',
                        shortCode: 'A',
                        description: 'Main headquarters building with offices, meeting rooms, and data center',
                        address: '123 Technology Drive, Innovation City, IC 12345',
                        isActive: true,
                        zoneCount: 2,
                        totalFloors: 3,
                        totalRooms: 4,
                        totalDoors: 2,
                        deviceCount: 10,
                        eventCount: 23,
                        hasAlerts: false,
                        createdAt: '2025-09-21T09:52:36.320484',
                        updatedAt: '2025-09-21T09:52:36.320484',
                        zonesUrl: '/api/v1/buildings/1/zones',
                        floorsUrl: '/api/v1/buildings/1/floors',
                        devicesUrl: '/api/v1/buildings/1/devices',
                    },
                ],
                total_count: 2,
                returned_count: 2,
                pagination: {
                    limit: params.limit || 100,
                    offset: params.offset || 0,
                    has_more: false,
                },
            },
        };
        return Promise.resolve(mockResponse);
    }
}

// Export singleton instance
export const buildingService = BuildingService.getInstance();
