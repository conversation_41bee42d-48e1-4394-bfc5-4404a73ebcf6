// Building Entity
export interface Building {
    id: number;
    name: string;
    shortCode: string;
    description: string;
    address: string;
    isActive: boolean;
    zoneCount: number;
    totalFloors: number;
    totalRooms: number;
    totalDoors: number;
    deviceCount: number;
    eventCount: number;
    hasAlerts: boolean;
    createdAt: string;
    updatedAt: string;
    zonesUrl: string;
    floorsUrl: string;
    devicesUrl: string;
}

// Pagination
export interface Pagination {
    limit: number;
    offset: number;
    has_more: boolean;
}

// API Response Data
export interface BuildingsData {
    buildings: Building[];
    total_count: number;
    returned_count: number;
    pagination: Pagination;
}

// Complete API response interface
export interface GetBuildingsApiResponse {
    response_code: string;
    response_message: string;
    response_message_ar: string;
    data: BuildingsData;
}

// Query parameters for filtering/pagination
export interface GetBuildingsQueryParams {
    limit?: number;
    offset?: number;
    is_active?: boolean;
    search?: string;
}
