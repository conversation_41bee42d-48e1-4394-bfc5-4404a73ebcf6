'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { SmartSpinnerInline } from '../components/common/SmartSpinner';

export default function Home() {
    const router = useRouter();
    const [isDarkMode, setIsDarkMode] = useState(false); // Start with false to prevent hydration mismatch
    const [isLoading, setIsLoading] = useState(true);
    const [stats, setStats] = useState({
        buildings: 0,
        sensors: 0,
        alerts: 0,
        efficiency: 0,
    });

    // Initialize dark mode from localStorage or system preference
    useEffect(() => {
        const initializeDarkMode = () => {
            const savedTheme = localStorage.getItem('darkMode');
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            const shouldBeDark = savedTheme ? JSON.parse(savedTheme) : prefersDark;

            setIsDarkMode(shouldBeDark);

            // Apply the theme immediately to prevent flashing
            if (shouldBeDark) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        };

        initializeDarkMode();
    }, []);

    // Animated counter effect
    useEffect(() => {
        // Loading delay - show spinner for 3 seconds
        const loadingTimer = setTimeout(() => {
            setIsLoading(false);
        }, 3000);

        const animateStats = () => {
            const targets = { buildings: 247, sensors: 1834, alerts: 12, efficiency: 98 };
            const duration = 2000;
            const steps = 60;
            const stepTime = duration / steps;

            let step = 0;
            const timer = setInterval(() => {
                step++;
                const progress = step / steps;

                setStats({
                    buildings: Math.floor(targets.buildings * progress),
                    sensors: Math.floor(targets.sensors * progress),
                    alerts: Math.floor(targets.alerts * progress),
                    efficiency: Math.floor(targets.efficiency * progress),
                });

                if (step >= steps) {
                    clearInterval(timer);
                    setStats(targets);
                }
            }, stepTime);
        };

        const timeout = setTimeout(animateStats, 3500); // Start stats animation after spinner
        return () => {
            clearTimeout(timeout);
            clearTimeout(loadingTimer);
        };
    }, []);

    // Theme toggle
    const toggleTheme = () => {
        const newDarkMode = !isDarkMode;
        setIsDarkMode(newDarkMode);

        // Update localStorage
        localStorage.setItem('darkMode', JSON.stringify(newDarkMode));

        // Update document class
        if (newDarkMode) {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
    };

    // Navigation handlers
    const navigateTo = (path: string) => {
        router.push(path);
    };

    return (
        <div
            className={`min-h-screen transition-colors duration-300 app-scroll ${isDarkMode ? 'bg-gray-900' : 'bg-gray-50'}`}>
            {/* Loading Spinner Overlay */}
            {isLoading && (
                <motion.div
                    className="fixed inset-0 z-50 flex items-center justify-center bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.3 }}>
                    <div className="flex flex-col items-center gap-6">
                        <SmartSpinnerInline size={80} speed={1.2} />
                        <motion.p
                            className="text-xl font-medium text-gray-700 dark:text-gray-300"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.5 }}>
                            Loading Nublar System...
                        </motion.p>
                    </div>
                </motion.div>
            )}

            {/* Animated Background */}
            <div className="fixed inset-0 overflow-hidden pointer-events-none">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-cyan-50 to-green-50 dark:from-gray-900 dark:via-blue-900/20 dark:to-cyan-900/20"></div>

                {/* Animated Grid */}
                <div className="absolute inset-0 opacity-30">
                    <div className="grid-pattern animate-pulse"></div>
                </div>

                {/* Floating Particles */}
                <div className="absolute inset-0">
                    {[...Array(20)].map((_, i) => (
                        <motion.div
                            key={i}
                            className="absolute w-2 h-2 bg-blue-400/30 rounded-full"
                            animate={{
                                x: [0, 100, 0],
                                y: [0, -100, 0],
                                opacity: [0, 1, 0],
                            }}
                            transition={{
                                duration: 8 + i * 0.5,
                                repeat: Infinity,
                                delay: i * 0.3,
                            }}
                            style={{
                                left: `${10 + ((i * 4) % 80)}%`,
                                top: `${20 + ((i * 3) % 60)}%`,
                            }}
                        />
                    ))}
                </div>
            </div>

            {/* Header */}
            <header className="relative z-10 p-4">
                <div className="max-w-7xl mx-auto flex justify-between items-center">
                    <div className="flex items-center space-x-3">
                        <Image
                            src="/svg/nublar-logo.svg"
                            alt="Nublar Sys Logo"
                            width={40}
                            height={40}
                            className="w-10 h-10"
                        />
                        <span className="text-2xl font-bold text-gray-900 dark:text-white">Nublar Sys</span>
                    </div>

                    <button
                        onClick={toggleTheme}
                        className="p-3 rounded-full bg-white/20 backdrop-blur-sm border border-white/30 hover:bg-white/30 transition-all duration-300 text-gray-900 dark:text-white">
                        {isDarkMode ? '☀️' : '🌙'}
                    </button>
                </div>
            </header>

            {/* Hero Section */}
            <section className="relative z-10 pt-8 pb-16">
                <div className="max-w-7xl mx-auto px-6 text-center">
                    <motion.h1
                        className="text-6xl md:text-7xl font-bold text-gray-900 dark:text-white mb-6"
                        initial={{ opacity: 0, y: 50 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8 }}>
                        Smart Systems for
                        <span className="bg-gradient-to-r from-blue-500 via-cyan-500 to-green-500 bg-clip-text text-transparent block">
                            Smart Living
                        </span>
                    </motion.h1>

                    <motion.p
                        className="text-xl md:text-2xl text-gray-700 dark:text-gray-300 mb-12 max-w-3xl mx-auto"
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8, delay: 0.2 }}>
                        Advanced analytics, real-time monitoring, and intelligent automation for modern smart cities and
                        building management systems.
                    </motion.p>

                    {/* Animated Stats */}
                    <motion.div
                        className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-8"
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8, delay: 0.4 }}>
                        <div className="text-center">
                            <div className="text-4xl font-bold text-blue-500 mb-2">{stats.buildings}</div>
                            <div className="text-gray-700 dark:text-gray-400">Smart Buildings</div>
                        </div>
                        <div className="text-center">
                            <div className="text-4xl font-bold text-cyan-500 mb-2">
                                {stats.sensors.toLocaleString()}
                            </div>
                            <div className="text-gray-700 dark:text-gray-400">Active Sensors</div>
                        </div>
                        <div className="text-center">
                            <div className="text-4xl font-bold text-orange-500 mb-2">{stats.alerts}</div>
                            <div className="text-orange-600 dark:text-orange-400">Active Alerts</div>
                        </div>
                        <div className="text-center">
                            <div className="text-4xl font-bold text-purple-500 mb-2">{stats.efficiency}%</div>
                            <div className="text-gray-700 dark:text-gray-400">Efficiency</div>
                        </div>
                    </motion.div>
                </div>
            </section>

            {/* Dashboard Navigation Cards */}
            <section className="relative z-10 py-12">
                <div className="max-w-7xl mx-auto px-6">
                    <motion.h2
                        className="text-4xl font-bold text-center text-gray-900 dark:text-white mb-12"
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8, delay: 0.6 }}>
                        System Dashboard
                    </motion.h2>

                    <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                        {[
                            {
                                title: 'Alerts Dashboard',
                                icon: '🚨',
                                path: '/alert-dashboard',
                                color: 'from-red-500 to-pink-500',
                            },
                            {
                                title: 'Building Management',
                                icon: '🏢',
                                path: '/dashboard',
                                color: 'from-green-500 to-emerald-500',
                            },
                            {
                                title: 'Analytics & Reports',
                                icon: '📊',
                                path: '/dashboard',
                                color: 'from-blue-500 to-cyan-500',
                            },
                            {
                                title: 'Tools & Settings',
                                icon: '⚙️',
                                path: '/dashboard',
                                color: 'from-purple-500 to-indigo-500',
                            },
                        ].map((card, index) => (
                            <motion.div
                                key={card.title}
                                className="group cursor-pointer"
                                initial={{ opacity: 0, y: 50 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8, delay: 0.8 + index * 0.1 }}
                                whileHover={{ scale: 1.05, y: -10 }}
                                onClick={() => navigateTo(card.path)}>
                                <div className="bg-white/20 backdrop-blur-sm border border-white/30 rounded-2xl p-6 h-full hover:bg-white/30 transition-all duration-300 hover:shadow-2xl hover:shadow-blue-500/25">
                                    <div
                                        className={`w-16 h-16 bg-gradient-to-r ${card.color} rounded-xl flex items-center justify-center text-2xl mb-4 group-hover:scale-110 transition-transform duration-300`}>
                                        {card.icon}
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                                        {card.title}
                                    </h3>
                                    <p className="text-gray-700 dark:text-gray-400">
                                        Access comprehensive monitoring and control features
                                    </p>
                                </div>
                            </motion.div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Tools Section */}
            <section className="relative z-10 py-12">
                <div className="max-w-7xl mx-auto px-6">
                    <motion.h2
                        className="text-4xl font-bold text-center text-gray-900 dark:text-white mb-12"
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8, delay: 1.2 }}>
                        Quick Tools
                    </motion.h2>

                    <div className="grid md:grid-cols-3 lg:grid-cols-6 gap-6">
                        {[
                            { title: 'Floor Plan Editor', icon: '🗺️', action: () => navigateTo('/floor-plan-editor') },
                            { title: 'Spinner Demo', icon: '⚡', action: () => navigateTo('/spinner-demo') },
                            { title: 'Statistics', icon: '📈', action: () => navigateTo('/dashboard') },
                            { title: 'Notifications', icon: '🔔', action: () => navigateTo('/alert-dashboard') },
                            { title: 'Reports', icon: '📋', action: () => navigateTo('/dashboard') },
                            { title: 'Settings', icon: '⚙️', action: () => navigateTo('/dashboard') },
                        ].map((tool, index) => (
                            <motion.div
                                key={tool.title}
                                className="group cursor-pointer"
                                initial={{ opacity: 0, scale: 0.8 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.5, delay: 1.4 + index * 0.1 }}
                                whileHover={{ scale: 1.1 }}
                                onClick={tool.action}>
                                <div className="bg-white/20 backdrop-blur-sm border border-white/30 rounded-xl p-4 text-center hover:bg-white/30 transition-all duration-300 hover:shadow-lg">
                                    <div className="text-3xl mb-3 group-hover:scale-125 transition-transform duration-300">
                                        {tool.icon}
                                    </div>
                                    <h4 className="text-sm font-medium text-gray-900 dark:text-white">{tool.title}</h4>
                                </div>
                            </motion.div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Footer */}
            <footer className="relative z-10 py-8 mt-12">
                <div className="max-w-7xl mx-auto px-6">
                    <div className="border-t border-white/20 pt-6">
                        <div className="flex flex-col md:flex-row justify-between items-center">
                            <div className="flex items-center space-x-3 mb-4 md:mb-0">
                                <Image
                                    src="/svg/nublar-logo.svg"
                                    alt="Nublar Sys Logo"
                                    width={32}
                                    height={32}
                                    className="w-8 h-8"
                                />
                                <span className="text-lg font-semibold text-gray-900 dark:text-white">
                                    Nublar Sys Platform
                                </span>
                            </div>
                            <div className="text-gray-700 dark:text-gray-400 text-sm">
                                © 2024 Nublar Sys. Advanced Building Management Solutions.
                            </div>
                        </div>
                    </div>
                </div>
            </footer>

            <style jsx>{`
                .grid-pattern {
                    background-image:
                        linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
                        linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
                    background-size: 50px 50px;
                    width: 100%;
                    height: 100%;
                }
            `}</style>
        </div>
    );
}
