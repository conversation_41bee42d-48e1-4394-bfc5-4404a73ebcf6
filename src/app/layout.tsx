import type { Metada<PERSON> } from 'next';
import { Geist, <PERSON>eist_Mono } from 'next/font/google';
import './globals.css';
import { EventsInitializer } from '@/components/features/alert-card/EventsInitializer';

const geistSans = Geist({
    variable: '--font-geist-sans',
    subsets: ['latin'],
});

const geistMono = Geist_Mono({
    variable: '--font-geist-mono',
    subsets: ['latin'],
});

export const metadata: Metadata = {
    title: 'Nublar Sys - AI Smart Building Management',
    description:
        'Advanced AI-powered building management solutions for smart systems and low current infrastructure. Monitor, control, and optimize your building operations with Nublar Sys.',
};

export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <html lang="en">
            <body
                className={`${geistSans.variable} ${geistMono.variable} antialiased `}
                suppressHydrationWarning={true}>
                <EventsInitializer /> {/* <-- starts auto refresh once on client */}
                {children}
            </body>
        </html>
    );
}
