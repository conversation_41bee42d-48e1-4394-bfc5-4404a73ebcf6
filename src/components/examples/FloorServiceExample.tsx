'use client';

import React, { useState } from 'react';
import { useFloors, useFloorsByBuilding, useFloor } from '@/infrastructure/api/floors';
import type { GetFloorsQueryParams } from '@/infrastructure/api/floors';

/**
 * Example component demonstrating how to use the Floor Service
 * This shows various ways to fetch and display floor data
 */
export default function FloorServiceExample() {
    const [selectedBuildingId, setSelectedBuildingId] = useState<number>(1);
    const [selectedFloorId, setSelectedFloorId] = useState<number | null>(null);
    const [queryParams, setQueryParams] = useState<GetFloorsQueryParams>({});

    // Example 1: Get all floors with custom parameters
    const {
        floors: allFloors,
        isLoading: allFloorsLoading,
        error: allFloorsError,
        totalCount,
        refetch: refetchAllFloors
    } = useFloors(queryParams, true);

    // Example 2: Get floors by building ID
    const {
        floors: buildingFloors,
        isLoading: buildingFloorsLoading,
        error: buildingFloorsError,
        refetch: refetchBuildingFloors
    } = useFloorsByBuilding(selectedBuildingId);

    // Example 3: Get single floor by ID
    const {
        floor: singleFloor,
        isLoading: singleFloorLoading,
        error: singleFloorError,
        refetch: refetchSingleFloor
    } = useFloor(selectedFloorId);

    const handleBuildingChange = (buildingId: number) => {
        setSelectedBuildingId(buildingId);
        setSelectedFloorId(null); // Reset selected floor when building changes
    };

    const handleFloorSelect = (floorId: number) => {
        setSelectedFloorId(floorId);
    };

    const handleQueryParamsChange = (newParams: Partial<GetFloorsQueryParams>) => {
        setQueryParams(prev => ({ ...prev, ...newParams }));
    };

    return (
        <div className="p-6 max-w-6xl mx-auto space-y-8">
            <div className="bg-white rounded-lg shadow-md p-6">
                <h1 className="text-2xl font-bold text-gray-900 mb-4">
                    Floor Service Example
                </h1>
                <p className="text-gray-600 mb-6">
                    This example demonstrates how to use the Floor Service to fetch and display floor data.
                    The service connects to the endpoint: <code className="bg-gray-100 px-2 py-1 rounded">http://localhost:8069/api/v1/floors</code>
                </p>
            </div>

            {/* Controls */}
            <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Controls</h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Building ID
                        </label>
                        <select
                            value={selectedBuildingId}
                            onChange={(e) => handleBuildingChange(Number(e.target.value))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            <option value={1}>Building 1</option>
                            <option value={2}>Building 2</option>
                            <option value={3}>Building 3</option>
                        </select>
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Floor Level Filter
                        </label>
                        <select
                            value={queryParams.level || ''}
                            onChange={(e) => handleQueryParamsChange({ 
                                level: e.target.value ? Number(e.target.value) : undefined 
                            })}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            <option value="">All Levels</option>
                            <option value={1}>Level 1</option>
                            <option value={2}>Level 2</option>
                            <option value={3}>Level 3</option>
                        </select>
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Actions
                        </label>
                        <button
                            onClick={() => refetchAllFloors()}
                            className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            Refresh All Floors
                        </button>
                    </div>
                </div>
            </div>

            {/* All Floors Section */}
            <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                    All Floors ({totalCount} total)
                </h2>
                {allFloorsLoading && <p className="text-blue-600">Loading all floors...</p>}
                {allFloorsError && <p className="text-red-600">Error: {allFloorsError}</p>}
                {allFloors.length > 0 && (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {allFloors.map((floor) => (
                            <div
                                key={floor.id}
                                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                                onClick={() => handleFloorSelect(floor.id)}
                            >
                                <h3 className="font-semibold text-gray-900">{floor.displayName}</h3>
                                <p className="text-sm text-gray-600">{floor.description}</p>
                                <div className="mt-2 text-xs text-gray-500">
                                    <p>Level: {floor.level} | Code: {floor.code}</p>
                                    <p>Rooms: {floor.totalRooms} | Devices: {floor.deviceCount}</p>
                                    <p>Building: {floor.building.name} | Zone: {floor.zone.name}</p>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>

            {/* Building Floors Section */}
            <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                    Floors in Building {selectedBuildingId}
                </h2>
                {buildingFloorsLoading && <p className="text-blue-600">Loading building floors...</p>}
                {buildingFloorsError && <p className="text-red-600">Error: {buildingFloorsError}</p>}
                {buildingFloors.length > 0 && (
                    <div className="space-y-2">
                        {buildingFloors.map((floor) => (
                            <div
                                key={floor.id}
                                className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50"
                            >
                                <div>
                                    <h4 className="font-medium text-gray-900">{floor.name}</h4>
                                    <p className="text-sm text-gray-600">Level {floor.level} - {floor.code}</p>
                                </div>
                                <div className="text-right text-sm text-gray-500">
                                    <p>{floor.totalRooms} rooms</p>
                                    <p>{floor.deviceCount} devices</p>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>

            {/* Single Floor Details */}
            {selectedFloorId && (
                <div className="bg-white rounded-lg shadow-md p-6">
                    <h2 className="text-xl font-semibold text-gray-900 mb-4">
                        Floor Details (ID: {selectedFloorId})
                    </h2>
                    {singleFloorLoading && <p className="text-blue-600">Loading floor details...</p>}
                    {singleFloorError && <p className="text-red-600">Error: {singleFloorError}</p>}
                    {singleFloor && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h3 className="font-semibold text-gray-900 mb-2">Basic Information</h3>
                                <dl className="space-y-1 text-sm">
                                    <div className="flex justify-between">
                                        <dt className="text-gray-600">Name:</dt>
                                        <dd className="text-gray-900">{singleFloor.name}</dd>
                                    </div>
                                    <div className="flex justify-between">
                                        <dt className="text-gray-600">Code:</dt>
                                        <dd className="text-gray-900">{singleFloor.code}</dd>
                                    </div>
                                    <div className="flex justify-between">
                                        <dt className="text-gray-600">Level:</dt>
                                        <dd className="text-gray-900">{singleFloor.level}</dd>
                                    </div>
                                    <div className="flex justify-between">
                                        <dt className="text-gray-600">Status:</dt>
                                        <dd className={singleFloor.isActive ? 'text-green-600' : 'text-red-600'}>
                                            {singleFloor.isActive ? 'Active' : 'Inactive'}
                                        </dd>
                                    </div>
                                </dl>
                            </div>
                            <div>
                                <h3 className="font-semibold text-gray-900 mb-2">Statistics</h3>
                                <dl className="space-y-1 text-sm">
                                    <div className="flex justify-between">
                                        <dt className="text-gray-600">Total Rooms:</dt>
                                        <dd className="text-gray-900">{singleFloor.totalRooms}</dd>
                                    </div>
                                    <div className="flex justify-between">
                                        <dt className="text-gray-600">Total Doors:</dt>
                                        <dd className="text-gray-900">{singleFloor.totalDoors}</dd>
                                    </div>
                                    <div className="flex justify-between">
                                        <dt className="text-gray-600">Device Count:</dt>
                                        <dd className="text-gray-900">{singleFloor.deviceCount}</dd>
                                    </div>
                                    <div className="flex justify-between">
                                        <dt className="text-gray-600">Event Count:</dt>
                                        <dd className="text-gray-900">{singleFloor.eventCount}</dd>
                                    </div>
                                </dl>
                            </div>
                        </div>
                    )}
                    <button
                        onClick={() => refetchSingleFloor()}
                        className="mt-4 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                    >
                        Refresh Floor Details
                    </button>
                </div>
            )}
        </div>
    );
}
