// app/EventsInitializer.tsx
'use client';

import { useEffect } from 'react';
import { useEventsStore } from '@/stores/alert.store';

export function EventsInitializer() {
    const startAutoRefresh = useEventsStore((s) => s.startAutoRefresh);
    const stopAutoRefresh = useEventsStore((s) => s.stopAutoRefresh);

    useEffect(() => {
        startAutoRefresh();
        return () => stopAutoRefresh();
    }, [startAutoRefresh, stopAutoRefresh]);

    return null; // nothing to render
}
