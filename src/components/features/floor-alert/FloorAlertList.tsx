import React, { useEffect } from 'react';
import FloorAlertItem, { FloorData } from './FloorAlertItem';
import { useEventsStore, type FloorAlertSummary } from '@/stores/alert.store';
import { useFloorStore } from '@/stores/floor.store';

interface FloorListProps {
    floorData?: FloorData[];
    onBuildingChange?: (building: string) => void;
    onFloorSelect?: (floorId: string) => void;
}

const FloorList: React.FC<FloorListProps> = ({ onFloorSelect }) => {
    // Alert floor store integration
    const {
        floorAlerts,
        // isLoading: alertsLoading,
        initialize: initializeAlerts,
        subscribeToBuildingStore: subscribeAlertsToBuilding,
    } = useEventsStore();

    // Floor store integration
    const {
        floors,
        // isLoading: floorsLoading,
        initialize: initializeFloors,
        subscribeToBuildingStore: subscribeFloorsToBuilding,
        selectedFloorId,
        setSelectedFloor,
    } = useFloorStore();

    // Initialize alert store and subscribe to building changes
    useEffect(() => {
        initializeAlerts();
        initializeFloors();
        subscribeAlertsToBuilding();
        subscribeFloorsToBuilding();
    }, [initializeAlerts, initializeFloors, subscribeAlertsToBuilding, subscribeFloorsToBuilding]);

    // Select initial floor if provided
    // useEffect(() => {
    //     if (initialSelectedFloor) {
    //         setSelectedFloor(Number(initialSelectedFloor));
    //     }
    // }, [initialSelectedFloor, setSelectedFloor]);

    // Handle floor click
    const handleFloorClick = (floorId: string) => {
        setSelectedFloor(Number(floorId));
        onFloorSelect?.(floorId);
    };

    // Convert floors to FloorData format
    const enhancedFloorData = floors.map((floor) => {
        const floorAlertSummary: FloorAlertSummary | undefined = floorAlerts.find(
            (alert) => alert.floorId === floor.id,
        );

        return {
            id: floor.id.toString(),
            name: floor.name,
            floorCode: floor.floorCode,
            alertCount: floorAlertSummary?.totalAlerts || 0,
            criticalAlerts: floorAlertSummary?.criticalAlerts || 0,
        };
    });

    return (
        <div className="w-[305px] bg-[#212633] border border-[#1F2937] rounded-lg p-3 flex flex-col gap-3 relative overflow-visible">
            {/* Floor List */}
            <div className="flex flex-col gap-2">
                {enhancedFloorData.map((floor) => (
                    <FloorAlertItem
                        key={floor.id}
                        floor={floor}
                        isSelected={selectedFloorId === Number(floor.id)}
                        onClick={handleFloorClick}
                    />
                ))}
            </div>

            {/* Loading indicator
            {(alertsLoading || floorsLoading) && (
                <div className="text-center text-gray-400 text-sm py-2">Loading floors and alerts...</div>
            )} */}
        </div>
    );
};

export default FloorList;
