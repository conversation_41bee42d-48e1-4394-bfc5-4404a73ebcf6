import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { logger } from '@/infrastructure/logging';
import type { Marker } from '@/infrastructure/api/markers/types';
import { getMarkersByBuildingAndFloor } from '@/infrastructure/api/markers/marker-service';
import { useFloorStore } from './floor.store';

// ---------------------- types ----------------------
export type MarkersStoreType = MarkersState & MarkersActions & MarkersExtra;

interface MarkersState {
    markers: Marker[];
    // events: Event[];
    isLoading: boolean;
}

interface MarkersActions {
    loadMarkers: () => Promise<void>;
    // loadEvents: () => Promise<void>; // ✅ new
}

interface MarkersExtra {
    subscribeToFloorStore: () => void;
}

const DEFAULT_STATE: MarkersState = {
    markers: [],
    // events: [],
    isLoading: false,
};

// ---------------------- store ----------------------
const _markersStore =
    (instanceId: string): StateCreator<MarkersStoreType> =>
    (set, get): MarkersStoreType => ({
        ...DEFAULT_STATE,

        loadMarkers: async () => {
            set({ isLoading: true });
            try {
                let selectedFloorId = useFloorStore.getState().selectedFloorId;

                // ✅ fallback: pick first available floor if none is selected
                if (!selectedFloorId) {
                    const floors = useFloorStore.getState().floors;
                    if (floors.length > 0) {
                        selectedFloorId = floors[0].id;
                        useFloorStore.getState().setSelectedFloor(selectedFloorId); // update store
                        logger.info(
                            `markersStore(${instanceId}): No floor selected, defaulting to floor=${selectedFloorId}`,
                        );
                    }
                }

                if (!selectedFloorId) {
                    logger.warn(`markersStore(${instanceId}): Still no floor available`);
                    set({ isLoading: false });
                    return;
                }

                const res = await getMarkersByBuildingAndFloor(1, selectedFloorId);

                set({
                    markers: res.markers,
                    isLoading: false,
                });

                logger.info(
                    `markersStore(${instanceId}): loaded markers for building=1, floor=${selectedFloorId}`,
                    res,
                );
            } catch (error) {
                logger.error(`markersStore(${instanceId}): loadMarkers error`, error as Error);
                set({ isLoading: false });
            }
        },

        // loadEvents: async () => {
        //     try {
        //         const res = await eventService.getEvents({ limit: 100 });
        //         const events = res.Events ?? [];
        //         set({ events }); // store raw events
        //     } catch (error) {
        //         console.error(`[markersStore] loadEvents error`, error);
        //     }
        // },

        subscribeToFloorStore: () => {
            useFloorStore.subscribe(
                (state) => state.selectedFloorId,
                (floorId) => {
                    if (floorId) {
                        get().loadMarkers();
                    }
                },
                { fireImmediately: true }, // ✅ run immediately with current floor
            );
        },
    });

// ---------------------- store instances -------------------
export const useMarkersStore = create<MarkersStoreType>()(
    subscribeWithSelector(devtools(_markersStore('global'), { name: 'markers-store-global' })),
);

export const createMarkersStore = (instanceId: string) =>
    create<MarkersStoreType>()(
        subscribeWithSelector(
            devtools(_markersStore(instanceId), {
                name: `markers-store-${instanceId}`,
            }),
        ),
    );
